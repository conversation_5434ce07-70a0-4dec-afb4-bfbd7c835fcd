services:
  # RedAI Affiliate MCP Server
  redai-affiliate:
    build:
      context: .
      dockerfile: src/server/redai_system/affiliatte/Dockerfile
    container_name: redai-affiliate-server
    ports:
      - "8004:8004"
    environment:
      - REDAI_AFFILIATE_API_BASE_URL=https://v2.redai.vn/api/v1
      - AFFILIATE_HTTP_HOST=0.0.0.0
      - AFFILIATE_HTTP_PORT=8004
      - AFFILIATE_HTTP_PATH=/mcp
      - AFFILIATE_TRANSPORT=streamable-http
    volumes:
      - ./logs/affiliate:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Blog MCP Server
  redai-blog:
    build:
      context: .
      dockerfile: src/server/redai_system/blog/Dockerfile
    container_name: redai-blog-server
    ports:
      - "8005:8005"
    environment:
      - REDAI_BLOG_API_BASE_URL=https://v2.redai.vn/api/v1
      - BLOG_HTTP_HOST=0.0.0.0
      - BLOG_HTTP_PORT=8005
      - BLOG_HTTP_PATH=/mcp
      - BLOG_TRANSPORT=streamable-http
    volumes:
      - ./logs/blog:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Model MCP Server
  redai-model:
    build:
      context: .
      dockerfile: src/server/redai_system/model/Dockerfile
    container_name: redai-model-server
    ports:
      - "8006:8006"
    environment:
      - REDAI_MODEL_API_BASE_URL=https://v2.redai.vn/api/v1
      - MODEL_HTTP_HOST=0.0.0.0
      - MODEL_HTTP_PORT=8006
      - MODEL_HTTP_PATH=/mcp
      - MODEL_TRANSPORT=streamable-http
    volumes:
      - ./logs/model:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8006/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Data MCP Server
  redai-data:
    build:
      context: .
      dockerfile: src/server/redai_system/data/Dockerfile
    container_name: redai-data-server
    ports:
      - "8007:8007"
    environment:
      - REDAI_DATA_API_BASE_URL=https://v2.redai.vn/api/v1
      - DATA_MODULE_HTTP_HOST=0.0.0.0
      - DATA_MODULE_HTTP_PORT=8007
      - DATA_MODULE_HTTP_PATH=/mcp
      - DATA_MODULE_TRANSPORT=streamable-http
    volumes:
      - ./logs/data:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8007/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Marketplace MCP Server
  redai-marketplace:
    build:
      context: .
      dockerfile: src/server/redai_system/marketplace/Dockerfile
    container_name: redai-marketplace-server
    ports:
      - "8008:8008"
    environment:
      - REDAI_MARKETPLACE_API_BASE_URL=https://v2.redai.vn/api/v1
      - MARKETPLACE_HTTP_HOST=0.0.0.0
      - MARKETPLACE_HTTP_PORT=8008
      - MARKETPLACE_HTTP_PATH=/mcp
      - MARKETPLACE_TRANSPORT=streamable-http
    volumes:
      - ./logs/marketplace:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Shipment MCP Server
  redai-shipment:
    build:
      context: .
      dockerfile: src/server/redai_system/shipment/Dockerfile
    container_name: redai-shipment-server
    ports:
      - "8009:8009"
    environment:
      - REDAI_SHIPMENT_API_BASE_URL=https://v2.redai.vn/api/v1
      - GHN_HTTP_HOST=0.0.0.0
      - GHN_HTTP_PORT=8009
      - GHN_HTTP_PATH=/mcp
      - SHIPMENT_TRANSPORT=streamable-http
      # Shipment API Keys (set these in .env file)
      - GHN_TOKEN=${GHN_TOKEN:-test}
      - GHN_SHOP_ID=${GHN_SHOP_ID:-test}
      - GHTK_TOKEN=${GHTK_TOKEN:-test}
      - GHTK_PARTNER_CODE=${GHTK_PARTNER_CODE:-test}
      - JT_USERNAME=${JT_USERNAME:-test}
      - JT_API_KEY=${JT_API_KEY:-test}
      - JT_CUSTOMER_CODE=${JT_CUSTOMER_CODE:-test}
      - AHAMOVE_API_KEY=${AHAMOVE_API_KEY:-test}
      - AHAMOVE_TOKEN=${AHAMOVE_TOKEN:-test}
      - AHAMOVE_MOBILE=${AHAMOVE_MOBILE:-test}
    volumes:
      - ./logs/shipment:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8009/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Business User Module MCP Server
  redai-business:
    build:
      context: .
      dockerfile: src/server/redai_system/business/Dockerfile
    container_name: redai-business-server
    ports:
      - "8010:8010"
    environment:
      - REDAI_BUSINESS_API_BASE_URL=https://v2.redai.vn/api/v1
      - BUSINESS_HTTP_HOST=0.0.0.0
      - BUSINESS_HTTP_PORT=8010
      - BUSINESS_HTTP_PATH=/mcp
      - BUSINESS_TRANSPORT=streamable-http
      # Business Module specific environment variables
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1
    volumes:
      - ./logs/business:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Marketing Module MCP Server
  redai-marketing:
    build:
      context: .
      dockerfile: src/server/redai_system/marketing/Dockerfile
    container_name: redai-marketing-server
    ports:
      - "8011:8011"
    environment:
      - REDAI_MARKETING_API_BASE_URL=https://v2.redai.vn/api/v1
      - MARKETING_HTTP_HOST=0.0.0.0
      - MARKETING_HTTP_PORT=8011
      - MARKETING_HTTP_PATH=/mcp
      - MARKETING_TRANSPORT=streamable-http
      # Marketing Module specific environment variables
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1
    volumes:
      - ./logs/marketing:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8011/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RedAI Workflow Module MCP Server
  redai-workflow:
    build:
      context: .
      dockerfile: src/server/redai_system/workflow/Dockerfile
    container_name: redai-workflow-server
    ports:
      - "8012:8012"
    environment:
      - REDAI_WORKFLOW_API_BASE_URL=https://v2.redai.vn/api/v1
      - WORKFLOW_HTTP_HOST=0.0.0.0
      - WORKFLOW_HTTP_PORT=8012
      - WORKFLOW_HTTP_PATH=/mcp
      - WORKFLOW_TRANSPORT=streamable-http
      # Workflow Module specific environment variables
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1
    volumes:
      - ./logs/workflow:/app/logs
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8012/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Google Sheets MCP Server
#  google-sheets:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/google/Dockerfile
#    container_name: google-sheets-server
#    ports:
#      - "8015:8015"
#    environment:
#      - GOOGLE_SHEETS_HTTP_HOST=0.0.0.0
#      - GOOGLE_SHEETS_HTTP_PORT=8015
#      - GOOGLE_SHEETS_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#    command: ["python", "src/server/redai_system/google/google_sheets_server.py"]
#    volumes:
#      - ./logs/google-sheets:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh", "8015"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

  # Google Docs MCP Server
#  google-docs:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/google/Dockerfile
#    container_name: google-docs-server
#    ports:
#      - "8016:8016"
#    environment:
#      - GOOGLE_DOCS_HTTP_HOST=0.0.0.0
#      - GOOGLE_DOCS_HTTP_PORT=8016
#      - GOOGLE_DOCS_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#    command: ["python", "src/server/redai_system/google/google_docs_server.py"]
#    volumes:
#      - ./logs/google-docs:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh", "8016"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

  # Google Drive MCP Server
#  google-drive:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/google/Dockerfile
#    container_name: google-drive-server
#    ports:
#      - "8017:8017"
#    environment:
#      - GOOGLE_DRIVE_HTTP_HOST=0.0.0.0
#      - GOOGLE_DRIVE_HTTP_PORT=8017
#      - GOOGLE_DRIVE_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#    command: ["python", "src/server/redai_system/google/google_drive_server.py"]
#    volumes:
#      - ./logs/google-drive:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh", "8017"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

  # Google Gmail MCP Server
#  google-gmail:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/google/Dockerfile
#    container_name: google-gmail-server
#    ports:
#      - "8018:8018"
#    environment:
#      - GOOGLE_GMAIL_HTTP_HOST=0.0.0.0
#      - GOOGLE_GMAIL_HTTP_PORT=8018
#      - GOOGLE_GMAIL_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#    command: ["python", "src/server/redai_system/google/google_gmail_server.py"]
#    volumes:
#      - ./logs/google-gmail:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh", "8018"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

  # Google Calendar MCP Server
#  google-calendar:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/google/Dockerfile
#    container_name: google-calendar-server
#    ports:
#      - "8019:8019"
#    environment:
#      - GOOGLE_CALENDAR_HTTP_HOST=0.0.0.0
#      - GOOGLE_CALENDAR_HTTP_PORT=8019
#      - GOOGLE_CALENDAR_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#    command: ["python", "src/server/redai_system/google/google_calendar_server.py"]
#    volumes:
#      - ./logs/google-calendar:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh", "8019"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

  # Google Ads MCP Server
#  google-ads:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/google/Dockerfile
#    container_name: google-ads-server
#    ports:
#      - "8020:8020"
#    environment:
#      - GOOGLE_ADS_HTTP_HOST=0.0.0.0
#      - GOOGLE_ADS_HTTP_PORT=8020
#      - GOOGLE_ADS_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#      # Google Ads API credentials (set these in your environment)
#      - GOOGLE_ADS_DEVELOPER_TOKEN=${GOOGLE_ADS_DEVELOPER_TOKEN}
#      - GOOGLE_ADS_LOGIN_CUSTOMER_ID=${GOOGLE_ADS_LOGIN_CUSTOMER_ID}
#    command: ["python", "src/server/redai_system/google/google_ads_server.py"]
#    volumes:
#      - ./logs/google-ads:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh", "8020"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

  # Google Analytics MCP Server
#  google-analytics:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/google/Dockerfile
#    container_name: google-analytics-server
#    ports:
#      - "8021:8021"
#    environment:
#      - GOOGLE_ANALYTICS_HTTP_HOST=0.0.0.0
#      - GOOGLE_ANALYTICS_HTTP_PORT=8021
#      - GOOGLE_ANALYTICS_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#    command: ["python", "src/server/redai_system/google/google_analytics_server.py"]
#    volumes:
#      - ./logs/google-analytics:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh", "8021"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

  # Redis for tool streaming
  redis:
    image: redis:7-alpine
    container_name: redai-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - redai-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Facebook Marketing MCP Server
#  facebook-marketing:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/facebook/Dockerfile
#    container_name: facebook-marketing-server
#    ports:
#      - "8022:8022"
#    environment:
#      - SERVER_TYPE=marketing
#      - FACEBOOK_MARKETING_HTTP_HOST=0.0.0.0
#      - FACEBOOK_MARKETING_HTTP_PORT=8022
#      - FACEBOOK_MARKETING_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#      # Facebook App credentials (set these in your environment)
#      - FACEBOOK_APP_ID=${FACEBOOK_APP_ID}
#      - FACEBOOK_APP_SECRET=${FACEBOOK_APP_SECRET}
#    volumes:
#      - ./logs/facebook-marketing:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

  # Facebook Page MCP Server
#  facebook-page:
#    build:
#      context: .
#      dockerfile: src/server/redai_system/facebook/Dockerfile
#    container_name: facebook-page-server
#    ports:
#      - "8023:8023"
#    environment:
#      - SERVER_TYPE=page
#      - FACEBOOK_PAGE_HTTP_HOST=0.0.0.0
#      - FACEBOOK_PAGE_HTTP_PORT=8023
#      - FACEBOOK_PAGE_HTTP_PATH=/mcp
#      - LOG_LEVEL=INFO
#      - PYTHONUNBUFFERED=1
#      # Facebook App credentials (set these in your environment)
#      - FACEBOOK_APP_ID=${FACEBOOK_APP_ID}
#      - FACEBOOK_APP_SECRET=${FACEBOOK_APP_SECRET}
#    volumes:
#      - ./logs/facebook-page:/app/logs
#    restart: unless-stopped
#    networks:
#      - redai-network
#    healthcheck:
#      test: ["CMD", "/app/healthcheck.sh"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 40s

networks:
  redai-network:
    name: project-1_redai-network
    external: true

volumes:
  logs:
    driver: local
  redis_data:
    driver: local
